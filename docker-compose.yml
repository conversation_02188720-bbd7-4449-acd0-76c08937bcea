
services:
  zhongji-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: zhongji-production-system
    ports:
      - "3000:3000"  # 前端端口
      - "8000:8000"  # 后端API端口
    environment:
      - NODE_ENV=production
      - PORT=8000
      - NEXT_PORT=3000
      - PYTHONUNBUFFERED=1
    volumes:
      # 可选：挂载数据目录用于持久化存储
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000", "&&", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - zhongji-network

  # 可选：添加数据库服务
  # database:
  #   image: postgres:15-alpine
  #   container_name: zhongji-database
  #   environment:
  #     POSTGRES_DB: zhongji
  #     POSTGRES_USER: zhongji_user
  #     POSTGRES_PASSWORD: zhongji_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   networks:
  #     - zhongji-network

networks:
  zhongji-network:
    driver: bridge

volumes:
  # postgres_data:
  data: 