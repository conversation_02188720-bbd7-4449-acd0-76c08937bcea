import React from "react";
import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import Script from "next/script";

export const metadata: Metadata = {
  title: "生产管理系统",
  description: "智能生产排程与人员排班管理系统",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <Script src="/env-config.js" strategy="beforeInteractive" />
      </head>
      <body suppressHydrationWarning>
        <Toaster position="top-center" reverseOrder={false} />
        <main>{children}</main>
      </body>
    </html>
  );
}