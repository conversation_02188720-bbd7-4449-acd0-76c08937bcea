{"name": "zhongji", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.510.0", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.46", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.4", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4.1.6", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3"}}