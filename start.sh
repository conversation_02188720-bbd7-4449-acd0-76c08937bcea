#!/bin/bash
set -e

echo "🚀 启动智能生产管理系统..."
echo "📦 前端: http://localhost:3000"
echo "🔧 后端: ${BACKEND_URL:-http://localhost:8000}"
echo ""

# 替换前端env-config.js里的BACKEND_URL变量
if [ -n "$BACKEND_URL" ]; then
  echo "替换前端配置 BACKEND_URL 为 $BACKEND_URL"
  sed -i "s|http://localhost:8000|$BACKEND_URL|g" /app/frontend/public/env-config.js
fi

# 启动后端服务（监听 8000）
cd /app/backend && python start.py &
BACKEND_PID=$!
echo "⚙️  后端服务已启动 (PID: $BACKEND_PID)"

# 等待后端启动
sleep 3

# 启动前端服务（监听 3000）
cd /app/frontend && PORT=3000 npm start &
FRONTEND_PID=$!
echo "🎨 前端服务已启动 (PID: $FRONTEND_PID)"

# 优雅关闭处理
cleanup() {
    echo "🛑 正在停止服务..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    echo "✅ 服务已停止"
    exit 0
}

trap cleanup SIGTERM SIGINT

# 等待服务
wait
